import React from 'react';
import { <PERSON><PERSON>, Toast } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined as ReloadOutlined,
  AddToSFilled,
  RefreshOutlined,
} from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import OptimizedImage from '@/components/OptimizedImage';
import { rateImage } from '@/apis/image';
import './index.less';

// 圆形进度条组件 - Circular progress component for image generation
const CircularProgress: React.FC<{progress: number}> = ({ progress }) => {
  return (
    <div className="circular-progress">
      <svg className="progress-ring" width="60" height="60">
        <circle
          className="progress-ring-circle"
          stroke="currentColor"
          strokeWidth="4"
          fill="transparent"
          r="26"
          cx="30"
          cy="30"
          style={{
            strokeDasharray: `${2 * Math.PI * 26}`,
            strokeDashoffset: `${2 * Math.PI * 26 * (1 - progress / 100)}`,
          }}
        />
      </svg>
      <div className="progress-text">{Math.round(progress)}%</div>
    </div>
  );
};

// Image information interface (should match the one in main page)
interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组 - Array containing 4 images
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean; // 添加点踩状态 - Add dislike state
}

interface ImageListProps {
  imageList: ImageInfo[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate: (imageInfo: ImageInfo) => void;
  onRefresh: () => void;
  progressMap: Record<string, number>;
  hasNextPage: boolean;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  loadImageList: (isLoadMore?: boolean) => void;
}

const ImageList: React.FC<ImageListProps> = ({
  imageList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  progressMap,
  hasNextPage,
  isLoadingMore,
  onLoadMore,
}) => {
  const isMobile = isMobileDevice();

  // Handle like/unlike - 处理点赞/取消点赞
  const handleLike = async (imageInfo: ImageInfo) => {
    try {
      const newLikedState = !imageInfo.liked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newLikedState ? 'like' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newLikedState ? '已点赞' : '已取消点赞',
        position: 'top',
        duration: 1,
      });
    } catch (err) {
      Toast.fail({
        content: '操作失败，请重试',
        position: 'top',
        duration: 2,
      });
    }
  };

  // Handle dislike/unlike - 处理点踩/取消点踩
  const handleDislike = async (imageInfo: ImageInfo) => {
    try {
      const newDislikedState = !imageInfo.disliked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newDislikedState ? 'dislike' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newDislikedState ? '已点踩' : '已取消点踩',
        position: 'top',
        duration: 1,
      });
    } catch (err) {
      Toast.fail({
        content: '操作失败，请重试',
        position: 'top',
        duration: 2,
      });
    }
  };

  // 计算模糊效果 - Calculate blur effect for progressive image generation
  const getBlurValue = (imageIndex: number, progress: number): number => {
    const imageProgress = Math.max(0, Math.min(100, (progress - imageIndex * 25) * 4));
    return Math.max(0, 20 - (imageProgress / 100) * 20);
  };

  // Render image item
  const renderImageItem = (imageInfo: ImageInfo) => {
    const progress = progressMap[imageInfo.uuid] || 0;
    const isGenerating = imageInfo.status === 'pending' || imageInfo.status === 'processing';
    const isFailed = imageInfo.status === 'failed';
    const isCompleted = imageInfo.status === 'completed';

    return (
      <div key={imageInfo.uuid} className="image-item">
        <div className="image-container">
          {isCompleted && (
            <div className="image-grid-completed">
              <div className="completion-text">已完成，请查看</div>
              <div className="image-grid">
                {imageInfo.imgUrls?.slice(0, 4).map((url, index) => (
                  <OptimizedImage
                    key={`${imageInfo.uuid}-${index}`}
                    src={url}
                    alt={`Generated image ${index + 1}`}
                    className="image-preview-grid"
                  />
                ))}
              </div>
            </div>
          )}

          {isGenerating && (
            <div className="image-generating">
              <div className="generating-overlay">
                <div className="generating-text">
                  正在根据你的要求生成图片... {Math.round(progress)}%
                </div>
                <CircularProgress progress={progress} />
                <div className="image-grid-generating">
                  {[0, 1, 2, 3].map((index) => (
                    <div
                      key={index}
                      className="image-placeholder"
                      style={{
                        filter: `blur(${getBlurValue(index, progress)}px)`,
                        opacity: progress > index * 25 ? 1 : 0.3,
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}

          {isFailed && (
            <div className="image-failed">
              <div className="failed-overlay">
                <div className="failed-text">生成失败</div>
                <div className="failed-reason">{imageInfo.errorMsg || '未知错误'}</div>
              </div>
            </div>
          )}
        </div>

        {/* Image info */}
        <div className="image-info">
          <div className="image-prompt">{imageInfo.prompt}</div>
          <div className="image-meta">
            <span className="image-size">{imageInfo.size}</span>
            <span className="image-time">
              {new Date(imageInfo.createAt).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Action buttons - 简化为点赞、点踩、重新生成三个按钮 */}
        {isCompleted && (
          <div className="image-actions">
            <Button
              size="small"
              className={`action-btn like-btn ${imageInfo.liked ? 'liked' : ''}`}
              onClick={() => handleLike(imageInfo)}
            >
              {imageInfo.liked ? <LikeFilled /> : <LikeOutlined />}
            </Button>

            <Button
              size="small"
              className={`action-btn dislike-btn ${imageInfo.disliked ? 'disliked' : ''}`}
              onClick={() => handleDislike(imageInfo)}
            >
              {imageInfo.disliked ? <DislikeFilled /> : <DislikeOutlined />}
            </Button>

            <Button
              size="small"
              className="action-btn regenerate-btn"
              onClick={() => onRegenerate(imageInfo)}
            >
              <ReloadOutlined />
            </Button>
          </div>
        )}
      </div>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <div className="empty-state">
      <div className="empty-icon">
        <AddToSFilled />
      </div>
      <div className="empty-title">还没有生成的图片</div>
      <div className="empty-description">
        点击下方按钮开始创建您的第一张AI图片
      </div>
      <Button
        type="primary"
        size="large"
        className="create-btn"
        onClick={onCreateNew}
      >
        开始创建
      </Button>
    </div>
  );

  // Render error state
  const renderErrorState = () => (
    <div className="error-state">
      <div className="error-title">加载失败</div>
      <div className="error-description">{error}</div>
      <Button
        type="primary"
        size="middle"
        className="retry-btn"
        onClick={onRefresh}
      >
        <RefreshOutlined />
        重试
      </Button>
    </div>
  );

  if (error) {
    return (
      <div className="image-list">
        {renderErrorState()}
      </div>
    );
  }

  if (!isLoading && imageList.length === 0) {
    return (
      <div className="image-list">
        {renderEmptyState()}
      </div>
    );
  }

  return (
    <div className="image-list">
      {/* Header */}
      <div className="list-header">
        <div className="header-title">生成历史</div>
        <Button
          size="small"
          className="refresh-btn"
          onClick={onRefresh}
        >
          <RefreshOutlined />
        </Button>
      </div>

      {/* Image grid */}
      <div className="image-grid">
        {imageList.map(renderImageItem)}
      </div>

      {/* Load more */}
      {hasNextPage && (
        <div className="load-more">
          <Button
            size="large"
            loading={isLoadingMore}
            onClick={onLoadMore}
            className="load-more-btn"
          >
            {isLoadingMore ? '加载中...' : '加载更多'}
          </Button>
        </div>
      )}

      {/* Create new button for mobile */}
      {isMobile && (
        <div className="mobile-create-btn">
          <Button
            type="primary"
            size="large"
            onClick={onCreateNew}
            className="create-new-btn"
          >
            <AddToSFilled />
            创建新图片
          </Button>
        </div>
      )}
    </div>
  );
};

export default ImageList;

@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.image-list {
  height: 100%;
  overflow-y: auto;
  padding: 24px;

  @media (max-width: 768px) {
    padding: 16px;
    padding-bottom: 80px; // Space for mobile create button
  }

  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: @common_level1_base_color;
    }

    .refresh-btn {
      color: @common_level2_base_color;
      border-color: @common_line_light_color;

      &:hover {
        color: @theme_primary1_color;
        border-color: @theme_primary1_color;
      }
    }
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  .image-item {
    background-color: @common_bg_z1_color;
    border: 1px solid @common_line_light_color;
    border-radius: @common_border_radius_l;
    overflow: hidden;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .image-container {
      position: relative;
      width: 100%;
      aspect-ratio: 1;
      background-color: @common_level5_base_color;
      overflow: hidden;

      // 完成状态的图片网格显示 - Completed state image grid display
      .image-grid-completed {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .completion-text {
          padding: 12px 16px;
          font-size: 14px;
          font-weight: 600;
          color: @common_level1_base_color;
          background-color: rgba(255, 255, 255, 0.9);
          text-align: center;
        }

        .image-grid {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;
          gap: 2px;

          .image-preview-grid {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      // 生成中状态显示 - Generating state display
      .image-generating,
      .image-failed {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: @common_level5_base_color;

        .generating-overlay,
        .failed-overlay {
          text-align: center;
          padding: 20px;
          width: 100%;
        }

        .generating-text {
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          color: @common_level1_base_color;
          margin-bottom: 16px;
        }

        // 圆形进度条样式 - Circular progress styles
        .circular-progress {
          position: relative;
          display: inline-block;
          margin-bottom: 20px;

          .progress-ring {
            transform: rotate(-90deg);

            .progress-ring-circle {
              transition: stroke-dashoffset 0.3s ease;
              stroke: @theme_primary1_color;
            }
          }

          .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            color: @theme_primary1_color;
          }
        }

        // 生成中的图片网格 - Generating image grid
        .image-grid-generating {
          display: grid;
          grid-template-columns: 1fr 1fr;
          grid-template-rows: 1fr 1fr;
          gap: 4px;
          width: 200px;
          height: 200px;

          .image-placeholder {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border-radius: 4px;
            transition: all 0.3s ease;
          }
        }

        .failed-text {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          color: @common_red1_color;
          margin-bottom: 8px;
        }

        .failed-reason {
          font-size: 12px;
          line-height: 16px;
          color: @common_level3_base_color;
        }
      }
    }

    .image-info {
      padding: 16px;

      .image-prompt {
        font-size: 14px;
        line-height: 20px;
        color: @common_level1_base_color;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .image-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        line-height: 16px;
        color: @common_level3_base_color;

        .image-size {
          background-color: @theme_primary3_color;
          color: @theme_primary1_color;
          padding: 2px 6px;
          border-radius: @common_border_radius_s;
          font-size: 10px;
        }
      }
    }

    .image-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      border-top: 1px solid @common_line_light_color;

      .action-btn {
        flex: 1;
        height: 36px;
        border-color: @common_line_light_color;
        color: @common_level2_base_color;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;

        &:hover {
          border-color: @theme_primary1_color;
          color: @theme_primary1_color;
        }

        // 点赞按钮样式 - Like button styles
        &.like-btn {
          &.liked {
            color: @common_red1_color;
            border-color: @common_red1_color;
            background-color: rgba(255, 14, 83, 0.1);
          }

          &:hover {
            color: @common_red1_color;
            border-color: @common_red1_color;
          }
        }

        // 点踩按钮样式 - Dislike button styles
        &.dislike-btn {
          &.disliked {
            color: @common_orange1_color;
            border-color: @common_orange1_color;
            background-color: rgba(255, 140, 0, 0.1);
          }

          &:hover {
            color: @common_orange1_color;
            border-color: @common_orange1_color;
          }
        }

        // 重新生成按钮样式 - Regenerate button styles
        &.regenerate-btn:hover {
          color: @common_blue1_color;
          border-color: @common_blue1_color;
        }
      }
    }
  }

  // Empty state
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      color: @common_level4_base_color;
      margin-bottom: 24px;
    }

    .empty-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: @common_level2_base_color;
      margin-bottom: 12px;
    }

    .empty-description {
      font-size: 14px;
      line-height: 20px;
      color: @common_level3_base_color;
      margin-bottom: 32px;
      max-width: 300px;
    }

    .create-btn {
      width: 200px;
      height: 48px;
    }
  }

  // Error state
  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;

    .error-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: @common_red1_color;
      margin-bottom: 12px;
    }

    .error-description {
      font-size: 14px;
      line-height: 20px;
      color: @common_level3_base_color;
      margin-bottom: 24px;
      max-width: 300px;
    }

    .retry-btn {
      width: 120px;
      height: 40px;
    }
  }

  // Load more
  .load-more {
    display: flex;
    justify-content: center;
    margin-top: 32px;

    .load-more-btn {
      width: 200px;
      height: 40px;
    }
  }

  // Mobile create button
  .mobile-create-btn {
    position: fixed;
    bottom: 20px;
    left: 16px;
    right: 16px;
    z-index: 100;

    .create-new-btn {
      width: 100%;
      height: 48px;
      border-radius: @common_border_radius_l;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// PC specific styles
.ai-image-pc-layout .ai-image-right-panel .image-list {
  .mobile-create-btn {
    display: none;
  }
}
